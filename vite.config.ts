import react from "@vitejs/plugin-react";
import { defineConfig } from "vite";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  css: {
    // Настройки для CSS
    devSourcemap: true,
  },
  optimizeDeps: {
    // Принудительная предварительная сборка для webgl-fluid-enhanced
    include: ["webgl-fluid-enhanced"],
    force: true,
  },
  build: {
    // Оптимизация сборки
    minify: "terser",
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
    rollupOptions: {
      output: {
        manualChunks: {
          react: ["react", "react-dom"],
          router: ["react-router-dom"],
          motion: ["framer-motion"],
          vendors: ["react-helmet-async", "react-scroll-parallax"],
          webgl: ["webgl-fluid-enhanced"],
        },
      },
    },
  },
  server: {
    // Настройки сервера разработки
    port: 3000,
    strictPort: false,
    open: true,
    fs: {
      // Разрешить доступ к файлам за пределами корня проекта
      allow: [".."],
    },
  },
});
