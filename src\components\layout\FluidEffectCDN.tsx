import { useEffect, useRef } from 'react';

interface FluidEffectCDNProps {
  className?: string;
}

const FluidEffectCDN: React.FC<FluidEffectCDNProps> = ({ className = '' }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    if (!canvasRef.current) return;

    // Загружаем библиотеку через CDN
    const loadFluidScript = async () => {
      try {
        // Проверяем, не загружена ли уже библиотека
        if ((window as any).webGLFluidEnhanced) {
          initFluid();
          return;
        }

        // Создаем script тег для загрузки библиотеки
        const script = document.createElement('script');
        script.type = 'module';
        script.innerHTML = `
          import webGLFluidEnhanced from 'https://esm.run/webgl-fluid-enhanced@latest';
          window.webGLFluidEnhanced = webGLFluidEnhanced;
          window.dispatchEvent(new CustomEvent('fluidLoaded'));
        `;
        
        document.head.appendChild(script);

        // Ждем загрузки библиотеки
        const handleFluidLoaded = () => {
          initFluid();
          window.removeEventListener('fluidLoaded', handleFluidLoaded);
        };

        window.addEventListener('fluidLoaded', handleFluidLoaded);

        // Очистка при размонтировании
        return () => {
          window.removeEventListener('fluidLoaded', handleFluidLoaded);
          if (script.parentNode) {
            script.parentNode.removeChild(script);
          }
        };
      } catch (error) {
        console.error('Ошибка загрузки webgl-fluid-enhanced через CDN:', error);
      }
    };

    const initFluid = () => {
      const webGLFluidEnhanced = (window as any).webGLFluidEnhanced;
      
      if (!canvasRef.current || !webGLFluidEnhanced) return;

      try {
        // Инициализация эффекта жидкости
        webGLFluidEnhanced.simulation(canvasRef.current, {
          SIM_RESOLUTION: 128,
          DYE_RESOLUTION: 1024,
          DENSITY_DISSIPATION: 0.97,
          VELOCITY_DISSIPATION: 0.97,
          PRESSURE: 0.8,
          PRESSURE_ITERATIONS: 20,
          CURL: 30,
          SPLAT_RADIUS: 0.3,
          SPLAT_FORCE: 6000,
          SHADING: true,
          COLORFUL: true,
          COLOR_UPDATE_SPEED: 10,
          COLOR_PALETTE: ['#3b82f6', '#6366f1', '#8b5cf6', '#a855f7', '#d946ef'],
          HOVER: true,
          TRANSPARENT: true,
          BRIGHTNESS: 0.5,
          BLOOM: true,
          BLOOM_ITERATIONS: 8,
          BLOOM_RESOLUTION: 256,
          BLOOM_INTENSITY: 0.8,
          BLOOM_THRESHOLD: 0.6,
          BLOOM_SOFT_KNEE: 0.7,
          SUNRAYS: true,
          SUNRAYS_RESOLUTION: 196,
          SUNRAYS_WEIGHT: 1.0,
          INITIAL: true,
          SPLAT_AMOUNT: 3,
        });

        console.log('Fluid effect initialized successfully via CDN');
      } catch (error) {
        console.error('Ошибка инициализации fluid effect:', error);
      }
    };

    loadFluidScript();
  }, []);

  return (
    <canvas
      ref={canvasRef}
      className={`absolute inset-0 w-full h-full z-0 ${className}`}
      style={{ pointerEvents: 'auto' }}
    />
  );
};

export default FluidEffectCDN;
