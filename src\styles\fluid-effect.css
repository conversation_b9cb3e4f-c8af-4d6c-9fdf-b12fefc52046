/* Стили для эффекта жидкости */
.fluid-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 0;
  pointer-events: none;
  overflow: hidden;
}

.fluid-container canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100% !important;
  height: 100% !important;
  pointer-events: auto;
  display: block;
}

/* Стили для мобильных устройств */
@media (max-width: 768px) {
  .fluid-container canvas {
    opacity: 0.7; /* Уменьшаем непрозрачность на мобильных устройствах */
  }
}

/* Оптимизация для тач-устройств */
@media (hover: none) {
  .fluid-container {
    pointer-events: auto; /* Включаем события касания на тач-устройствах */
  }
}
