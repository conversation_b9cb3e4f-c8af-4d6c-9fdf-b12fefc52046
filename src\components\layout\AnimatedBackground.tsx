import { useEffect, useRef } from 'react';

interface AnimatedBackgroundProps {
  className?: string;
}

const AnimatedBackground: React.FC<AnimatedBackgroundProps> = ({ className = '' }) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    // Создаем анимированные частицы
    const createParticles = () => {
      const container = containerRef.current;
      if (!container) return;

      // Очищаем существующие частицы
      container.innerHTML = '';

      const particleCount = 20;
      
      for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        
        // Случайные позиции и размеры
        const size = Math.random() * 100 + 50;
        const x = Math.random() * 100;
        const y = Math.random() * 100;
        const duration = Math.random() * 20 + 10;
        const delay = Math.random() * 5;
        
        particle.style.cssText = `
          position: absolute;
          width: ${size}px;
          height: ${size}px;
          left: ${x}%;
          top: ${y}%;
          background: radial-gradient(circle, rgba(59, 130, 246, 0.3) 0%, rgba(99, 102, 241, 0.2) 50%, transparent 70%);
          border-radius: 50%;
          filter: blur(2px);
          animation: float ${duration}s ease-in-out infinite ${delay}s alternate;
          pointer-events: none;
        `;
        
        container.appendChild(particle);
      }
    };

    createParticles();

    // Пересоздаем частицы при изменении размера окна
    const handleResize = () => {
      createParticles();
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return (
    <>
      <style>{`
        @keyframes float {
          0% {
            transform: translateY(0px) translateX(0px) scale(1);
            opacity: 0.7;
          }
          50% {
            transform: translateY(-20px) translateX(10px) scale(1.1);
            opacity: 0.9;
          }
          100% {
            transform: translateY(-40px) translateX(-10px) scale(0.9);
            opacity: 0.5;
          }
        }
        
        @keyframes gradient-shift {
          0% {
            background-position: 0% 50%;
          }
          50% {
            background-position: 100% 50%;
          }
          100% {
            background-position: 0% 50%;
          }
        }
        
        .animated-bg {
          background: linear-gradient(-45deg, 
            rgba(59, 130, 246, 0.1), 
            rgba(99, 102, 241, 0.1), 
            rgba(139, 92, 246, 0.1), 
            rgba(168, 85, 247, 0.1)
          );
          background-size: 400% 400%;
          animation: gradient-shift 15s ease infinite;
        }
      `}</style>
      <div 
        ref={containerRef}
        className={`absolute inset-0 w-full h-full z-0 animated-bg ${className}`}
        style={{ pointerEvents: 'none' }}
      />
    </>
  );
};

export default AnimatedBackground;
