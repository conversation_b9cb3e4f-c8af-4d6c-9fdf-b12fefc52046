import { useEffect, useRef } from "react";

interface FluidEffectDirectProps {
  className?: string;
}

const FluidEffectDirect: React.FC<FluidEffectDirectProps> = ({ className = "" }) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    let cleanup: (() => void) | null = null;

    // Динамический импорт библиотеки
    const initFluid = async () => {
      try {
        const module = await import("webgl-fluid-enhanced");
        console.log("Модуль загружен (Direct):", module);

        const WebGLFluidEnhanced: any = module.default;
        console.log("WebGLFluidEnhanced класс (Direct):", WebGLFluidEnhanced);

        if (!containerRef.current || !WebGLFluidEnhanced) {
          throw new Error("WebGLFluidEnhanced класс недоступен");
        }

        // Создаем canvas программно
        const canvas = document.createElement("canvas");
        canvas.style.cssText = `
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          pointer-events: auto;
          display: block;
          z-index: 1;
          background: transparent;
        `;

        // Добавляем canvas в контейнер
        containerRef.current.appendChild(canvas);

        // Добавляем тестовый стиль для отладки
        canvas.style.border = "2px solid red";
        console.log("Canvas добавлен в DOM с красной рамкой для тестирования");

        // Ждем немного
        await new Promise((resolve) => setTimeout(resolve, 100));

        // Проверяем размеры
        const rect = containerRef.current.getBoundingClientRect();
        console.log("Container размеры (Direct):", rect);

        // Устанавливаем размеры canvas
        canvas.width = rect.width || window.innerWidth;
        canvas.height = rect.height || window.innerHeight;

        console.log("Canvas размеры установлены:", canvas.width, "x", canvas.height);

        // Проверяем поддержку WebGL
        const gl = canvas.getContext("webgl") || canvas.getContext("experimental-webgl");
        if (!gl) {
          console.error("WebGL не поддерживается в этом браузере!");
          return;
        }
        console.log("WebGL поддерживается:", gl);

        // Тестируем canvas простым рисованием
        const ctx2d = canvas.getContext("2d");
        if (ctx2d) {
          ctx2d.fillStyle = "rgba(255, 0, 0, 0.5)";
          ctx2d.fillRect(0, 0, 100, 100);
          console.log("Тестовый красный квадрат нарисован на canvas");
        }

        // Создаем экземпляр класса с canvas
        const fluidInstance = new WebGLFluidEnhanced(canvas);
        console.log("Fluid instance создан (Direct):", fluidInstance);

        // Настраиваем конфигурацию для лучшей видимости
        const fluidConfig = {
          simResolution: 128,
          dyeResolution: 512,
          densityDissipation: 0.95,
          velocityDissipation: 0.95,
          pressure: 0.8,
          pressureIterations: 20,
          curl: 30,
          splatRadius: 0.4,
          splatForce: 8000,
          shading: true,
          colorful: true,
          colorUpdateSpeed: 15,
          colorPalette: ["#ff0000", "#00ff00", "#0000ff", "#ffff00", "#ff00ff"],
          hover: true,
          transparent: true,
          backgroundColor: "#000000",
          brightness: 0.8,
          bloom: true,
          bloomIterations: 8,
          bloomResolution: 256,
          bloomIntensity: 1.0,
          bloomThreshold: 0.4,
          bloomSoftKnee: 0.7,
          sunrays: false,
          sunraysResolution: 196,
          sunraysWeight: 1.0,
        };

        // Устанавливаем конфигурацию
        fluidInstance.setConfig(fluidConfig);

        // Запускаем симуляцию
        fluidInstance.start();

        // Добавляем начальные splats для тестирования
        setTimeout(() => {
          if (fluidInstance && typeof fluidInstance.multipleSplats === "function") {
            fluidInstance.multipleSplats(5);
            console.log("Добавлены начальные splats (Direct)");
          }
        }, 1000);

        console.log("Fluid effect запущен успешно (Direct)");

        cleanup = () => {
          // Останавливаем симуляцию при размонтировании
          if (fluidInstance && typeof fluidInstance.stop === "function") {
            fluidInstance.stop();
          }
          // Удаляем canvas
          if (canvas.parentNode) {
            canvas.parentNode.removeChild(canvas);
          }
          console.log("Fluid effect остановлен (Direct)");
        };
      } catch (error) {
        console.error("Ошибка загрузки webgl-fluid-enhanced (Direct):", error);
      }
    };

    initFluid();

    // Очистка при размонтировании компонента
    return () => {
      if (cleanup) {
        cleanup();
      }
    };
  }, []);

  return (
    <div
      ref={containerRef}
      className={`absolute inset-0 w-full h-full z-0 ${className}`}
      style={{
        pointerEvents: "none",
        width: "100%",
        height: "100%",
        overflow: "hidden",
      }}
    />
  );
};

export default FluidEffectDirect;
