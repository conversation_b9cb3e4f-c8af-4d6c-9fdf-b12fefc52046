import { useEffect, useRef } from 'react';
import webGLFluidEnhanced from 'webgl-fluid-enhanced';

interface FluidEffectProps {
  className?: string;
  config?: Record<string, any>;
}

const FluidEffect: React.FC<FluidEffectProps> = ({ className = '', config = {} }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    if (!canvasRef.current) return;

    // Настройка эффекта жидкости с оптимальными параметрами
    webGLFluidEnhanced.simulation(canvasRef.current, {
      SIM_RESOLUTION: 128,
      DYE_RESOLUTION: 1024,
      DENSITY_DISSIPATION: 0.98,
      VELOCITY_DISSIPATION: 0.98,
      PRESSURE: 0.8,
      PRESSURE_ITERATIONS: 20,
      CURL: 30,
      SPLAT_RADIUS: 0.25,
      SPLAT_FORCE: 6000,
      SHADING: true,
      COLORFUL: true,
      COLOR_UPDATE_SPEED: 10,
      COLOR_PALETTE: ['#3b82f6', '#6366f1', '#8b5cf6', '#a855f7', '#d946ef'],
      HOVER: true,
      TRANSPARENT: true,
      BRIGHTNESS: 0.5,
      BLOOM: true,
      BLOOM_ITERATIONS: 8,
      BLOOM_RESOLUTION: 256,
      BLOOM_INTENSITY: 0.8,
      BLOOM_THRESHOLD: 0.6,
      BLOOM_SOFT_KNEE: 0.7,
      SUNRAYS: true,
      SUNRAYS_RESOLUTION: 196,
      SUNRAYS_WEIGHT: 1.0,
      ...config
    });

    // Очистка при размонтировании компонента
    return () => {
      // К сожалению, в библиотеке нет метода для очистки,
      // но мы можем удалить canvas при необходимости
    };
  }, [config]);

  return (
    <canvas 
      ref={canvasRef} 
      className={`absolute inset-0 w-full h-full z-0 ${className}`}
      style={{ pointerEvents: 'auto' }}
    />
  );
};

export default FluidEffect;