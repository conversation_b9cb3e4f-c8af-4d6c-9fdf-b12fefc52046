import { useEffect, useRef } from "react";

interface FluidEffectProps {
  className?: string;
  config?: Record<string, any>;
}

const FluidEffect: React.FC<FluidEffectProps> = ({ className = "", config = {} }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    if (!canvasRef.current) return;

    let cleanup: (() => void) | null = null;

    // Динамический импорт библиотеки
    const initFluid = async () => {
      try {
        const module = await import("webgl-fluid-enhanced");
        console.log("Модуль загружен:", module);

        const WebGLFluidEnhanced: any = module.default;
        console.log("WebGLFluidEnhanced класс:", WebGLFluidEnhanced);

        if (!canvasRef.current || !WebGLFluidEnhanced) {
          throw new Error("WebGLFluidEnhanced класс недоступен");
        }

        // Ждем немного, чтобы canvas был готов
        await new Promise((resolve) => setTimeout(resolve, 100));

        // Проверяем размеры canvas
        const rect = canvasRef.current.getBoundingClientRect();
        console.log("Canvas размеры:", rect);

        if (rect.width === 0 || rect.height === 0) {
          console.warn("Canvas имеет нулевые размеры, ждем...");
          await new Promise((resolve) => setTimeout(resolve, 500));
        }

        // Создаем экземпляр класса с canvas как контейнером
        const fluidInstance = new WebGLFluidEnhanced(canvasRef.current);
        console.log("Fluid instance создан:", fluidInstance);

        // Настраиваем конфигурацию
        const fluidConfig = {
          simResolution: 128,
          dyeResolution: 1024,
          densityDissipation: 0.98,
          velocityDissipation: 0.98,
          pressure: 0.8,
          pressureIterations: 20,
          curl: 30,
          splatRadius: 0.25,
          splatForce: 6000,
          shading: true,
          colorful: true,
          colorUpdateSpeed: 10,
          colorPalette: ["#3b82f6", "#6366f1", "#8b5cf6", "#a855f7", "#d946ef"],
          hover: true,
          transparent: true,
          brightness: 0.5,
          bloom: true,
          bloomIterations: 8,
          bloomResolution: 256,
          bloomIntensity: 0.8,
          bloomThreshold: 0.6,
          bloomSoftKnee: 0.7,
          sunrays: true,
          sunraysResolution: 196,
          sunraysWeight: 1.0,
          ...config,
        };

        // Устанавливаем конфигурацию
        fluidInstance.setConfig(fluidConfig);

        // Запускаем симуляцию
        fluidInstance.start();

        // Добавляем начальные splats для тестирования
        setTimeout(() => {
          if (fluidInstance && typeof fluidInstance.multipleSplats === "function") {
            fluidInstance.multipleSplats(3);
            console.log("Добавлены начальные splats");
          }
        }, 1000);

        console.log("Fluid effect запущен успешно");

        cleanup = () => {
          // Останавливаем симуляцию при размонтировании
          if (fluidInstance && typeof fluidInstance.stop === "function") {
            fluidInstance.stop();
          }
          console.log("Fluid effect остановлен");
        };
      } catch (error) {
        console.error("Ошибка загрузки webgl-fluid-enhanced:", error);
      }
    };

    initFluid();

    // Очистка при размонтировании компонента
    return () => {
      if (cleanup) {
        cleanup();
      }
    };
  }, [config]);

  return (
    <canvas
      ref={canvasRef}
      className={`absolute inset-0 w-full h-full z-0 ${className}`}
      style={{
        pointerEvents: "auto",
        width: "100%",
        height: "100%",
        display: "block",
      }}
    />
  );
};

export default FluidEffect;
