import { useEffect, useRef } from 'react';

const SimpleFluidTest: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    if (!canvasRef.current) return;

    const testFluid = async () => {
      try {
        // Простой тест canvas
        const canvas = canvasRef.current;
        if (!canvas) return;

        // Устанавливаем размеры
        canvas.width = window.innerWidth;
        canvas.height = 400;

        // Тестируем 2D контекст
        const ctx = canvas.getContext('2d');
        if (ctx) {
          ctx.fillStyle = 'blue';
          ctx.fillRect(50, 50, 100, 100);
          console.log("2D тест успешен - синий квадрат должен быть виден");
        }

        // Тестируем WebGL
        const gl = canvas.getContext('webgl');
        if (gl) {
          gl.clearColor(1.0, 0.0, 0.0, 1.0);
          gl.clear(gl.COLOR_BUFFER_BIT);
          console.log("WebGL тест успешен - красный фон должен быть виден");
        }

        // Пробуем загрузить библиотеку
        const module = await import('webgl-fluid-enhanced');
        const WebGLFluidEnhanced = module.default;
        
        console.log("Библиотека загружена для простого теста:", WebGLFluidEnhanced);

        // Очищаем canvas перед инициализацией fluid
        if (ctx) {
          ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        // Создаем экземпляр
        const fluid = new (WebGLFluidEnhanced as any)(canvas);
        
        // Простая конфигурация
        fluid.setConfig({
          simResolution: 64,
          dyeResolution: 256,
          densityDissipation: 0.9,
          velocityDissipation: 0.9,
          pressure: 0.8,
          pressureIterations: 20,
          curl: 30,
          splatRadius: 0.5,
          splatForce: 10000,
          shading: false,
          colorful: true,
          colorUpdateSpeed: 20,
          colorPalette: ["#ff0000", "#00ff00", "#0000ff"],
          hover: true,
          transparent: false,
          backgroundColor: "#000000",
          brightness: 1.0,
          bloom: false,
          sunrays: false,
        });

        fluid.start();
        console.log("Простой fluid тест запущен");

        // Добавляем splats через 1 секунду
        setTimeout(() => {
          if (fluid && typeof fluid.multipleSplats === 'function') {
            fluid.multipleSplats(5);
            console.log("Добавлены тестовые splats");
          }
        }, 1000);

      } catch (error) {
        console.error("Ошибка в простом тесте:", error);
      }
    };

    testFluid();
  }, []);

  return (
    <div style={{ 
      position: 'fixed', 
      top: 0, 
      left: 0, 
      width: '100%', 
      height: '400px', 
      zIndex: 1000,
      border: '3px solid green',
      background: 'black'
    }}>
      <canvas
        ref={canvasRef}
        style={{
          width: '100%',
          height: '100%',
          display: 'block',
          border: '2px solid yellow'
        }}
      />
      <div style={{
        position: 'absolute',
        top: '10px',
        left: '10px',
        color: 'white',
        background: 'rgba(0,0,0,0.7)',
        padding: '5px',
        fontSize: '12px'
      }}>
        Простой тест Fluid Effect
      </div>
    </div>
  );
};

export default SimpleFluidTest;
